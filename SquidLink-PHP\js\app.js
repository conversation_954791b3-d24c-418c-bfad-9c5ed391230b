// SquidLink PHP - Main JavaScript
// Exact match to React version functionality

class SquidLinkApp {
    constructor() {
        this.currentName = 'FAHIM';
        this.nameIndex = 0;
        this.names = ['FAHIM', 'HASAN', 'SANTO'];
        this.isRedirecting = false;
        this.redirectingTo = '';
        this.playerNumber = 456;
        this.audioContext = null;
        this.sounds = {};
        this.socialUrls = {
            'Facebook': 'https://www.instagram.com/_fahimsanto/',
            'LinkedIn': 'https://www.linkedin.com/in/fahim-hasan-santo-583987267/',
            'Instagram': 'https://www.facebook.com/fahim.hasan.santo.2024'
        };

        this.init();
    }

    init() {
        this.setupCursor();
        this.setupScrollProgress();
        this.setupParticles();
        this.setupNameCycling();
        this.setupProjectRedirects();
        this.setupSocialRedirects();
        this.setupNFC();
        this.setupAudio();
        this.updatePlayerNumber();
        this.setupHoverSounds();
        this.setupNavigationSymbols();
        this.setupDigitalClock();
        this.setupSmoothScrolling();
        this.setupMobileOptimizations();
    }

    setupCursor() {
        // Only setup custom cursor on non-mobile devices
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) ||
                         (navigator.maxTouchPoints > 0);

        if (!isMobile) {
            const cursor = document.createElement('div');
            cursor.className = 'squid-cursor';
            document.body.appendChild(cursor);

            document.addEventListener('mousemove', (e) => {
                cursor.style.left = e.clientX + 'px';
                cursor.style.top = e.clientY + 'px';
            });

            // Cursor hover effects
            const hoverElements = document.querySelectorAll('a, button, .card-3d, .skill-card');
            hoverElements.forEach(el => {
                el.addEventListener('mouseenter', () => {
                    cursor.style.transform = 'scale(1.5)';
                    this.playSound('hover');
                });
                el.addEventListener('mouseleave', () => {
                    cursor.style.transform = 'scale(1)';
                });
            });
        }
    }

    setupNavigationSymbols() {
        const symbols = document.querySelectorAll('nav .text-squid-pink span');
        symbols.forEach(symbol => {
            symbol.addEventListener('click', () => {
                this.playSound('hover');
                symbol.style.animation = 'glitch 0.5s ease-in-out';
                setTimeout(() => {
                    symbol.style.animation = '';
                }, 500);
            });
        });
    }

    setupScrollProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        document.body.appendChild(progressBar);

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });
    }

    setupParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 50 },
                    color: { value: ['#ff0055', '#00ffaa', '#ffffff'] },
                    shape: {
                        type: ['circle', 'triangle', 'polygon'],
                        polygon: { nb_sides: 6 }
                    },
                    opacity: {
                        value: 0.3,
                        random: true
                    },
                    size: {
                        value: 3,
                        random: true,
                        anim: {
                            enable: true,
                            speed: 2,
                            size_min: 0.3
                        }
                    },
                    move: {
                        enable: true,
                        speed: 1,
                        direction: 'top',
                        random: true,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: {
                            enable: true,
                            mode: 'repulse'
                        },
                        onclick: {
                            enable: true,
                            mode: 'push'
                        }
                    },
                    modes: {
                        repulse: {
                            distance: 100,
                            duration: 0.4
                        },
                        push: {
                            particles_nb: 4
                        }
                    }
                },
                retina_detect: true
            });
        }
    }

    setupNameCycling() {
        const nameElement = document.querySelector('.glitch-text');
        if (nameElement) {
            setInterval(() => {
                this.nameIndex = (this.nameIndex + 1) % this.names.length;
                this.currentName = this.names[this.nameIndex];
                nameElement.textContent = this.currentName;
                nameElement.setAttribute('data-text', this.currentName);
            }, 3000);
        }
    }

    setupProjectRedirects() {
        const projectCards = document.querySelectorAll('.project-card');
        const projects = [
            { name: 'Jashore Sell Bazar', url: 'https://jashoresellbazar.com' },
            { name: 'Athlete Bazaar', url: 'https://athletebazaar.com' },
            { name: 'Shadhin Alo', url: 'https://shadhinalo.com' }
        ];

        projectCards.forEach((card, index) => {
            // Improve mobile touch handling
            const handleCardClick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (projects[index]) {
                    this.handleProjectClick(projects[index]);
                }
            };

            card.addEventListener('click', handleCardClick);
            card.addEventListener('touchend', handleCardClick, { passive: false });

            // Add mobile-friendly styling
            card.style.cursor = 'pointer';
            card.style.webkitTapHighlightColor = 'transparent';
        });
    }

    handleProjectClick(project) {
        if (this.isRedirecting) return;

        this.isRedirecting = true;
        this.redirectingTo = project.name;
        this.playSound('hover');

        this.showPremiumLoading(project.name);

        setTimeout(() => {
            window.open(project.url, '_blank');
            this.hidePremiumLoading();
            this.isRedirecting = false;
        }, 2000);
    }

    setupSocialRedirects() {
        const socialLinks = document.querySelectorAll('.social-link');
        const socials = [
            { name: 'Instagram', url: 'https://www.instagram.com/_fahimsanto/' },
            { name: 'LinkedIn', url: 'https://www.linkedin.com/fahim-hasan-santo-583987267/' },
            { name: 'Facebook', url: 'https://www.facebook.com/fahim.hasan.santo.2024' }
        ];

        socialLinks.forEach((link, index) => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (socials[index]) {
                    this.handleSocialClick(socials[index]);
                }
            });
        });
    }

    handleSocialClick(social) {
        if (this.isRedirecting) return;

        this.isRedirecting = true;
        this.redirectingTo = social.name;
        this.playSound('hover');

        this.showPremiumLoading(social.name);

        setTimeout(() => {
            window.open(social.url, '_blank');
            this.hidePremiumLoading();
            this.isRedirecting = false;
        }, 2000);
    }

    showPremiumLoading(destination) {
        const overlay = document.createElement('div');
        overlay.className = 'premium-loading-overlay';
        overlay.innerHTML = `
            <!-- Corner Circles -->
            <div class="corner-circle top-left"></div>
            <div class="corner-circle top-right"></div>
            <div class="corner-circle bottom-left"></div>
            <div class="corner-circle bottom-right"></div>

            <div class="loading-content">
                <div class="loading-logo">
                    <div class="squid-logo">
                        <div class="logo-circle"></div>
                        <div class="logo-triangle"></div>
                        <div class="logo-square"></div>
                    </div>
                </div>
                <div class="loading-text">REDIRECTING TO</div>
                <div class="loading-destination">${destination.toUpperCase()}</div>
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    hidePremiumLoading() {
        const overlay = document.querySelector('.premium-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    setupNFC() {
        const nfcButton = document.querySelector('.nfc-button');
        if (nfcButton) {
            // Handle both click and touch events
            const handleNFCTrigger = (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.triggerNFCAnimation();
            };

            // Add multiple event listeners for better mobile support
            nfcButton.addEventListener('click', handleNFCTrigger);
            nfcButton.addEventListener('touchstart', handleNFCTrigger, { passive: false });

            // Prevent touch issues on mobile
            nfcButton.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, { passive: false });

            nfcButton.addEventListener('touchmove', (e) => {
                e.preventDefault();
            }, { passive: false });
        }
    }

    triggerNFCAnimation() {
        // Create NFC animation overlay
        const nfcOverlay = document.createElement('div');
        nfcOverlay.className = 'nfc-animation-overlay';
        nfcOverlay.innerHTML = `
            <div class="nfc-content">
                <div class="nfc-card">📱 NFC CARD DETECTED</div>
                <div class="nfc-player">PLAYER 456 VERIFIED</div>
                <div class="nfc-status">ACCESS GRANTED</div>
            </div>
        `;
        document.body.appendChild(nfcOverlay);

        setTimeout(() => {
            nfcOverlay.remove();
        }, 4000);
    }

    setupAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
        }
    }

    playSound(type) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        if (type === 'hover') {
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
        }

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    setupHoverSounds() {
        const hoverElements = document.querySelectorAll('a, button, .card-3d, .skill-card, .project-card');
        hoverElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                this.playSound('hover');
            });
        });
    }

    updatePlayerNumber() {
        const playerNumberElement = document.querySelector('.digital-display');
        if (playerNumberElement) {
            playerNumberElement.textContent = `PLAYER ${this.playerNumber}`;
        }
    }

    setupDigitalClock() {
        const updateClock = () => {
            const now = new Date();

            // Format time
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            const timeString = `${hours}:${minutes}:${seconds}`;

            // Format date
            const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                           'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

            const dayName = days[now.getDay()];
            const monthName = months[now.getMonth()];
            const date = now.getDate().toString().padStart(2, '0');
            const dateString = `${dayName} ${monthName} ${date}`;

            // Update display
            const timeElement = document.getElementById('digital-time');
            const dateElement = document.getElementById('digital-date');

            if (timeElement) timeElement.textContent = timeString;
            if (dateElement) dateElement.textContent = dateString;
        };

        // Update immediately and then every second
        updateClock();
        setInterval(updateClock, 1000);
    }

    setupSmoothScrolling() {
        // Handle navigation links
        const navLinks = document.querySelectorAll('nav a[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupMobileOptimizations() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) ||
                         (navigator.maxTouchPoints > 0);

        if (isMobile) {
            // Add mobile class to body
            document.body.classList.add('mobile-device');

            // Disable hover effects on mobile
            const hoverElements = document.querySelectorAll('.card-3d, .project-card, .skill-card');
            hoverElements.forEach(el => {
                el.style.transition = 'transform 0.2s ease';
            });

            // Improve touch targets
            const touchTargets = document.querySelectorAll('button, a, .nfc-button');
            touchTargets.forEach(target => {
                target.style.minHeight = '44px';
                target.style.minWidth = '44px';
                target.style.webkitTapHighlightColor = 'transparent';
            });

            // Fix viewport issues
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }

            // Prevent zoom on input focus
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });
            });
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SquidLinkApp();
});

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => console.log('SW registered'))
            .catch(registrationError => console.log('SW registration failed'));
    });
}