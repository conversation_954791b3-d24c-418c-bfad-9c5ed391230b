/* EXACT COPY FROM REACT VERSION - SquidLink CSS */

/* SQUID GAME FONT */
@font-face {
  font-family: 'Game Of Squids';
  src: url('../fonts/Game Of Squids.woff2') format('woff2'),
       url('../fonts/Game Of Squids.woff') format('woff'),
       url('../fonts/Game Of Squids.ttf') format('truetype'),
       url('../fonts/Game Of Squids.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}





:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 10%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 5%);
  --popover-foreground: hsl(0, 0%, 95%);
  --card: hsl(0, 0%, 5%);
  --card-foreground: hsl(0, 0%, 95%);
  --border: hsl(340, 100%, 35%);
  --input: hsl(0, 0%, 10%);
  --primary: hsl(340, 100%, 35%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(150, 100%, 35%);
  --secondary-foreground: hsl(0, 0%, 0%);
  --accent: hsl(150, 100%, 35%);
  --accent-foreground: hsl(0, 0%, 0%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 100%, 100%);
  --ring: hsl(340, 100%, 35%);
  --radius: 0.5rem;
  
  /* Squid Game Colors */
  --squid-pink: rgb(255, 0, 85);
  --squid-green: hsl(150, 100%, 35%);
  --squid-red: hsl(348, 83%, 47%);
  --electric-blue: hsl(195, 100%, 50%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-color: var(--border);
}

h2.text-4xl.md\:text-5xl.font-bold.text-white.mb-8 {
    text-align: center;
}

/* SMOOTH SCROLLING */
html {
  scroll-behavior: smooth;
}

html::-webkit-scrollbar {
  width: 8px;
}

.bg-gray-900\/50 {
    background-color: rgb(17 24 39 / 0%) !important;
} 
html::-webkit-scrollbar-track {
  background: #1a1a1a;
}

html::-webkit-scrollbar-thumb {
  background: rgb(255, 0, 85);
  border-radius: 4px;
}

html::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 0, 85, 0.8);
}
.top-4 {
    top: 8rem !important;
}
body {
  font-family: 'Poppins', sans-serif;
  background: var(--background);
  color: var(--foreground);
  cursor: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Game Of Squids', 'Bebas Neue', cursive;
}

/* SQUID GAME FONT FOR BOLD/HEADER ELEMENTS */
.font-bold,
.font-bebas,
.digital-display,
.loading-text,
.loading-destination,
.nfc-card,
button {
  font-family: 'Game Of Squids', 'Bebas Neue', cursive !important;
}





/* PARTICLES BACKGROUND INTEGRATION */
#particles-js {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: transparent;
  pointer-events: none;
}

/* EXACT COMPONENT STYLES FROM REACT */
.squid-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--squid-pink);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* PREMIUM SMOOTH HOVER ANIMATIONS */
.card-3d {
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
}

.card-3d:hover {
  transform: perspective(1000px) rotateX(-5deg) rotateY(5deg) scale(1.05);
  box-shadow:
    0 20px 40px rgba(255, 0, 85, 0.3),
    0 10px 20px rgba(0, 0, 0, 0.4);
}

/* PROJECT CARD PLAYER TEXT POSITIONING */
.project-card .absolute.top-4.left-4,
.skill-card .absolute.top-4.left-4 {
  position: absolute !important;
  top: 1rem !important;
  left: 1rem !important;
  z-index: 10;
  line-height: 1.1;
  white-space: nowrap;
}

.project-card .absolute.top-4.right-4,
.skill-card .absolute.top-4.right-4 {
  position: absolute !important;
  top: 1rem !important;
  right: 1rem !important;
  z-index: 10;
}

/* SCROLL ANIMATION FOR NAVIGATION */
#main-nav {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(0);
}

/* Initial state - navigation is visible but normal */
#main-nav.initial {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(0);
}

/* Hidden state when scrolling down */
#main-nav.hidden {
  transform: translateY(-100%);
  opacity: 0;
}

/* Visible state when scrolling up or at top */
#main-nav.visible {
  transform: translateY(0);
  opacity: 1;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Scrolled state - smaller navigation */
#main-nav.scrolled {
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-container {
  padding: 0.5rem 0;
}

.nav-symbols {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-symbols {
  font-size: 1.25rem;
  transform: scale(0.8);
}

.nav-player-display {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-player-display {
  transform: translateX(-50%) scale(0.7);
  min-width: 70px !important;
}

#main-nav.scrolled .nav-player-display .digital-display {
  font-size: 1rem !important;
  padding: 0.3rem 0.5rem !important;
}

.nav-clock {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-clock {
  transform: scale(0.75);
}

.nav-toggle {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#main-nav.scrolled .nav-toggle {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 0.8rem;
}

/* PREMIUM NAVIGATION HOVER EFFECTS */
.nav-symbols a {
  position: relative;
  display: inline-block;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.nav-symbols a::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 0, 85, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.nav-symbols a:hover::before {
  width: 60px;
  height: 60px;
}

.nav-symbols a:hover {
  transform: scale(1.2) rotate(5deg);
  color: #ffffff;
  text-shadow:
    0 0 10px rgba(255, 0, 85, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(255, 0, 85, 0.4);
  filter: brightness(1.3);
}

.nav-symbols a:active {
  transform: scale(1.1) rotate(-2deg);
  transition: all 0.1s ease;
}

/* Premium glow effect for navigation symbols */
.nav-symbols a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(255, 0, 85, 0.1) 50%,
    transparent 100%);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.nav-symbols a:hover::after {
  opacity: 1;
  transform: scale(1.3);
  animation: pulseGlow 1.5s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 5px rgba(255, 0, 85, 0.3);
  }
  to {
    box-shadow: 0 0 20px rgba(255, 0, 85, 0.6), 0 0 30px rgba(255, 0, 85, 0.3);
  }
}

/* PREMIUM DARK MODE TOGGLE HOVER */
.nav-toggle {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-toggle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.nav-toggle:hover::before {
  width: 80px;
  height: 80px;
}

.nav-toggle:hover {
  transform: scale(1.15) rotate(10deg);
  background: linear-gradient(135deg, #00ff88 0%, #ff0055 100%);
  box-shadow:
    0 0 15px rgba(0, 255, 136, 0.5),
    0 0 25px rgba(255, 0, 85, 0.3),
    0 4px 15px rgba(0, 0, 0, 0.3);
  filter: brightness(1.2);
}

.nav-toggle:active {
  transform: scale(1.05) rotate(-5deg);
  transition: all 0.1s ease;
}

/* Premium ripple effect for toggle */
.nav-toggle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease-out;
  opacity: 0;
}

.nav-toggle:hover::after {
  width: 100px;
  height: 100px;
  opacity: 1;
  animation: rippleEffect 1s ease-out infinite;
}

@keyframes rippleEffect {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* PREMIUM DIGITAL PLAYER DISPLAY HOVER */
.digital-player-display {
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.digital-player-display:hover {
  transform: translateX(-50%) scale(1.05);
  filter: brightness(1.2) saturate(1.3);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.6),
    0 0 40px rgba(59, 130, 246, 0.4),
    0 8px 25px rgba(0, 0, 0, 0.3);
}

.digital-player-display:hover .digital-display {
  color: #ffffff !important;
  text-shadow:
    0 0 10px rgba(255, 0, 85, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(255, 0, 85, 0.4);
  animation: digitalGlow 1.5s ease-in-out infinite alternate;
}

.digital-player-display:hover .bg-black {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
  border-color: rgba(255, 0, 85, 0.5);
}

@keyframes digitalGlow {
  from {
    text-shadow:
      0 0 10px rgba(255, 0, 85, 0.8),
      0 0 20px rgba(255, 0, 85, 0.6);
  }
  to {
    text-shadow:
      0 0 15px rgba(255, 0, 85, 1),
      0 0 25px rgba(255, 0, 85, 0.8),
      0 0 35px rgba(255, 0, 85, 0.6);
  }
}

/* VOLUME CONTROL STYLING */
.volume-control {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 0, 85, 0.3);
  border-radius: 25px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.volume-control:hover {
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(255, 0, 85, 0.3);
}

.volume-icon {
  font-size: 18px;
  animation: musicPulse 2s ease-in-out infinite;
}

@keyframes musicPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #ff0055, #00ff88);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(255, 0, 85, 0.4);
  transition: all 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(255, 0, 85, 0.6);
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #ff0055, #00ff88);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(255, 0, 85, 0.4);
}

/* Mobile responsive volume control */
@media (max-width: 768px) {
  .volume-control {
    bottom: 15px;
    right: 15px;
    padding: 8px 12px;
  }

  .volume-slider {
    width: 60px;
  }
}

/* SQUID GAME INTRO TEXT ANIMATION */
.squid-intro-text {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.squid-intro-text .letter {
  display: inline-block;
  opacity: 0;
  transform: translateY(100px) rotateX(90deg);
  animation: squidGameIntro 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  text-shadow:
    0 0 20px rgba(255, 0, 85, 0.8),
    0 0 40px rgba(255, 0, 85, 0.6),
    0 0 60px rgba(255, 0, 85, 0.4);
}

@keyframes squidGameIntro {
  0% {
    opacity: 0;
    transform: translateY(100px) rotateX(90deg) scale(0.5);
    text-shadow: none;
  }
  30% {
    opacity: 0.3;
    transform: translateY(50px) rotateX(45deg) scale(0.8);
  }
  60% {
    opacity: 0.7;
    transform: translateY(-10px) rotateX(-10deg) scale(1.1);
    text-shadow:
      0 0 15px rgba(255, 0, 85, 0.6),
      0 0 30px rgba(255, 0, 85, 0.4);
  }
  80% {
    opacity: 0.9;
    transform: translateY(5px) rotateX(5deg) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) rotateX(0deg) scale(1);
    text-shadow:
      0 0 20px rgba(255, 0, 85, 0.8),
      0 0 40px rgba(255, 0, 85, 0.6),
      0 0 60px rgba(255, 0, 85, 0.4);
  }
}

/* Squid Game style glitch effect for letters */
.squid-intro-text .letter.glitch {
  animation: squidGameIntro 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards,
             letterGlitch 0.3s ease-in-out 0.8s;
}

@keyframes letterGlitch {
  0%, 100% {
    transform: translateY(0) rotateX(0deg) scale(1);
  }
  20% {
    transform: translateY(-2px) rotateX(2deg) scale(1.02);
    color: #ff0055;
  }
  40% {
    transform: translateY(1px) rotateX(-1deg) scale(0.98);
    color: #00ff88;
  }
  60% {
    transform: translateY(-1px) rotateX(1deg) scale(1.01);
    color: #ffffff;
  }
  80% {
    transform: translateY(0.5px) rotateX(-0.5deg) scale(0.99);
    color: #ff0055;
  }
}

/* Enhanced name cycling with Squid Game style */
.name-cycling {
  position: relative;
}

.name-cycling::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 0, 85, 0.1) 20%,
    rgba(255, 0, 85, 0.2) 50%,
    rgba(255, 0, 85, 0.1) 80%,
    transparent 100%);
  opacity: 0;
  animation: nameGlow 3s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes nameGlow {
  0%, 100% {
    opacity: 0;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scaleX(1.2);
  }
}

/* Squid Game style text reveal */
.text-reveal {
  overflow: hidden;
  position: relative;
}

.text-reveal::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.9) 50%,
    transparent 100%);
  animation: textReveal 2s ease-out forwards;
}

@keyframes textReveal {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* PREMIUM SLIDE ANIMATIONS */
@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  70% {
    transform: translateY(5px);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

/* Apply animations to navigation states */
#main-nav.visible {
  animation: slideInFromTop 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

#main-nav.hidden {
  animation: slideOutToTop 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Enhance the glow effect when navigation appears */
#main-nav.visible::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 0, 85, 0.1) 50%,
    transparent 100%);
  animation: navGlow 2s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: -1;
}

@keyframes navGlow {
  from {
    opacity: 0.3;
    transform: scaleX(0.8);
  }
  to {
    opacity: 0.6;
    transform: scaleX(1.2);
  }
}

/* PROJECT CARD PREMIUM HOVER */
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  filter: brightness(1) saturate(1);
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
  filter: brightness(1.1) saturate(1.2);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 15px 30px rgba(255, 0, 85, 0.2);
}

.project-card:active {
  transform: translateY(-4px) scale(1.01);
  transition: all 0.1s ease;
}

/* BUTTON PREMIUM HOVER EFFECTS */
.btn, button, .social-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  filter: brightness(1);
}

.btn:hover, button:hover {
  transform: translateY(-2px) scale(1.05);
  filter: brightness(1.2);
  box-shadow:
    0 10px 25px rgba(255, 0, 85, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:active, button:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* SOCIAL LINKS HOVER */
.social-link:hover {
  transform: translateY(-3px) scale(1.1);
  filter: brightness(1.3) saturate(1.5);
  box-shadow:
    0 8px 20px rgba(255, 0, 85, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
}

/* GUARD FIGURE HOVER */
.guard-figure:hover {
  transform: scale(1.05) rotate(2deg);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.guard-figure:hover .guard-eye-left,
.guard-figure:hover .guard-eye-right {
  animation: red-light-bloom 1s ease-in-out infinite;
  box-shadow:
    0 0 15px rgb(255, 0, 85),
    0 0 30px rgb(255, 0, 85),
    0 0 45px rgb(255, 0, 85);
}

/* PREMIUM LOADING OVERLAY - EXACT NODE.JS MATCH */
.premium-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-in-out;
}

/* Corner Circles */
.corner-circle {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 3px solid rgb(255, 0, 85);
  border-radius: 50%;
  animation: cornerPulse 2s ease-in-out infinite;
}

.corner-circle.top-left {
  top: 20px;
  left: 20px;
}

.corner-circle.top-right {
  top: 20px;
  right: 20px;
}

.corner-circle.bottom-left {
  bottom: 20px;
  left: 20px;
}

.corner-circle.bottom-right {
  bottom: 20px;
  right: 20px;
}

.loading-content {
  text-align: center;
  color: white;
  animation: slideUp 0.5s ease-out;
}

.loading-logo {
  margin: 0 auto 3rem;
}

.squid-logo {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  position: relative;
  border: 3px solid rgb(255, 0, 85);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: logoSpin 3s linear infinite;
}

.logo-circle {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgb(255, 0, 85);
  border-radius: 50%;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.logo-triangle {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 25px solid rgb(255, 0, 85);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.logo-square {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgb(255, 0, 85);
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.loading-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ff88;
  margin-bottom: 1rem;
  letter-spacing: 3px;
}

.loading-destination {
  font-size: 2rem;
  font-weight: bold;
  color: rgb(255, 0, 85);
  margin-bottom: 2rem;
  text-shadow: 0 0 20px rgb(255, 0, 85);
  letter-spacing: 2px;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.loading-dots span {
  width: 12px;
  height: 12px;
  background: #00ff88;
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.3s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes cornerPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 0, 85, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 20px rgba(255, 0, 85, 0);
  }
}

@keyframes logoSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}

/* NFC ANIMATION OVERLAY */
.nfc-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 85, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: nfcFlash 4s ease-in-out;
}

.nfc-content {
  text-align: center;
  color: white;
  animation: nfcPulse 4s ease-in-out;
}

.nfc-card {
  font-size: 3rem;
  font-weight: bold;
  color: rgb(255, 0, 85);
  margin-bottom: 1rem;
  text-shadow: 0 0 20px rgb(255, 0, 85);
  animation: nfcGlow 1s ease-in-out infinite alternate;
}

.nfc-player {
  font-size: 2rem;
  color: #00ff88;
  margin-bottom: 1rem;
  text-shadow: 0 0 15px #00ff88;
}

.nfc-status {
  font-size: 1.5rem;
  color: white;
  opacity: 0.9;
}

@keyframes nfcFlash {
  0%, 100% { background: rgba(255, 0, 85, 0.1); }
  25%, 75% { background: rgba(255, 0, 85, 0.3); }
  50% { background: rgba(255, 0, 85, 0.5); }
}

@keyframes nfcPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes nfcGlow {
  from {
    text-shadow: 0 0 20px rgb(255, 0, 85);
    filter: brightness(1);
  }
  to {
    text-shadow: 0 0 40px rgb(255, 0, 85), 0 0 60px rgb(255, 0, 85);
    filter: brightness(1.3);
  }
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

.nfc-pulse {
  animation: pulse-glow 1s ease-in-out infinite;
}

.guard-figure {
  width: 8rem;
  height: 12rem;
  position: relative;
}

.guard-body {
  width: 100%;
  height: 100%;
  background: rgb(255, 0, 85);
  border-radius: 4rem 4rem 0rem 0rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.guard-mask-triangular {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  border: 2px solid white;
}

.guard-mask-round {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-round::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  border-radius: 50%;
}

.guard-eyes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 0.3rem;
  z-index: 10;
}

.guard-eye-left, .guard-eye-right {
  width: 0.4rem;
  height: 0.4rem;
  background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
  border-radius: 50%;
  animation: red-light-bloom 2s ease-in-out infinite;
  box-shadow:
    0 0 8px rgb(255, 0, 85),
    0 0 16px rgb(255, 0, 85),
    0 0 24px rgb(255, 0, 85);
}

.squid-shape {
  width: 4rem;
  height: 4rem;
  background: var(--squid-pink);
  border-radius: 50% 50% 0 50%;
  position: relative;
  transform: rotate(45deg);
}

.squid-shape::before {
  content: '';
  position: absolute;
  width: 1rem;
  height: 2rem;
  background: var(--squid-pink);
  border-radius: 50%;
  top: -0.5rem;
  left: 50%;
  transform: translateX(-50%) rotate(-45deg);
}

.squid-shape::after {
  content: '';
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
  top: 1rem;
  left: 1rem;
  transform: rotate(-45deg);
}

/* EXACT KEYFRAME ANIMATIONS FROM REACT */
@keyframes glitch {
  0% { transform: translate(0) }
  20% { transform: translate(-2px, 2px) }
  40% { transform: translate(-2px, -2px) }
  60% { transform: translate(2px, 2px) }
  80% { transform: translate(2px, -2px) }
  100% { transform: translate(0) }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px var(--squid-pink), 0 0 10px var(--squid-pink), 0 0 15px var(--squid-pink); }
  100% { box-shadow: 0 0 10px var(--squid-pink), 0 0 20px var(--squid-pink), 0 0 30px var(--squid-pink); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes liquid {
  0% { border-radius: 50%; transform: scale(1); }
  50% { border-radius: 60% 40% 30% 70%; transform: scale(1.1); }
  100% { border-radius: 50%; transform: scale(1); }
}

@keyframes guard-sway {
  0%, 100% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
}

@keyframes number-flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite alternate;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

/* EXACT GLITCH TEXT FROM REACT */
.glitch-text {
  position: relative;
  animation: glitch-main 3s infinite;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(0, 255, 136, 0.4);
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.glitch-text::before {
  animation: glitch-1 0.3s infinite;
  color: rgb(255, 0, 85);
  z-index: -1;
  text-shadow: 2px 0 0 rgb(255, 0, 85);
}

.glitch-text::after {
  animation: glitch-2 0.3s infinite;
  color: #00ff88;
  z-index: -2;
  text-shadow: -2px 0 0 #00ff88;
}

/* EXACT GLITCH KEYFRAMES FROM REACT */
@keyframes glitch-main {
  0%, 90%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-5px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-2px, -5px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(5px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-3px, 3px) scale(1.01);
    filter: hue-rotate(45deg);
  }
  60% {
    transform: translate(3px, -3px) scale(0.99);
    filter: hue-rotate(135deg);
  }
  70% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(225deg);
  }
  80% {
    transform: translate(4px, 1px);
    filter: hue-rotate(315deg);
  }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 8%, 0 8%);
    opacity: 0.8;
  }
  10% {
    transform: translate(-4px, 3px);
    clip-path: polygon(0 12%, 100% 12%, 100% 22%, 0 22%);
    opacity: 0.9;
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.7;
  }
  30% {
    transform: translate(4px, -2px);
    clip-path: polygon(0 38%, 100% 38%, 100% 48%, 0 48%);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 51%, 100% 51%, 100% 61%, 0 61%);
    opacity: 0.9;
  }
  50% {
    transform: translate(3px, 4px);
    clip-path: polygon(0 64%, 100% 64%, 100% 74%, 0 74%);
    opacity: 0.6;
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 77%, 100% 77%, 100% 87%, 0 87%);
    opacity: 0.8;
  }
  70% {
    transform: translate(-4px, -1px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  80% {
    transform: translate(2px, -4px);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  90% {
    transform: translate(-1px, 3px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.7;
  }
  15% {
    transform: translate(3px, -4px);
    clip-path: polygon(0 18%, 100% 18%, 100% 28%, 0 28%);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 31%, 100% 31%, 100% 41%, 0 41%);
    opacity: 0.6;
  }
  35% {
    transform: translate(-3px, 3px);
    clip-path: polygon(0 44%, 100% 44%, 100% 54%, 0 54%);
    opacity: 0.9;
  }
  45% {
    transform: translate(4px, 2px);
    clip-path: polygon(0 57%, 100% 57%, 100% 67%, 0 67%);
    opacity: 0.7;
  }
  55% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  65% {
    transform: translate(-4px, -2px);
    clip-path: polygon(0 83%, 100% 83%, 100% 93%, 0 93%);
    opacity: 0.6;
  }
  75% {
    transform: translate(1px, 4px);
    clip-path: polygon(0 2%, 100% 2%, 100% 12%, 0 12%);
    opacity: 0.9;
  }
  85% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 96%, 100% 96%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  95% {
    transform: translate(3px, -1px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.8;
  }
}

@keyframes red-light-bloom {
  0%, 100% {
    box-shadow:
      0 0 8px rgb(255, 0, 85),
      0 0 16px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85);
    opacity: 0.8;
  }
  50% {
    box-shadow:
      0 0 12px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85),
      0 0 36px rgb(255, 0, 85);
    opacity: 1;
  }
}

/* ESSENTIAL UTILITY CLASSES */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }
.pointer-events-none { pointer-events: none; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.text-white { color: white; }
.text-squid-pink { color: var(--squid-pink); }
.text-squid-green { color: var(--squid-green); }
.text-squid-red { color: var(--squid-red); }
.bg-black { background-color: black; }
.bg-white { background-color: white; }
.bg-squid-pink { background-color: var(--squid-pink); }
.bg-squid-green { background-color: var(--squid-green); }
.bg-card { background-color: var(--card); }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-squid-pink { border-color: var(--squid-pink); }
.border-squid-green { border-color: var(--squid-green); }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-liquid { animation: liquid 4s ease-in-out infinite; }
.animate-guard-sway { animation: guard-sway 3s ease-in-out infinite; }
.animate-number-flip { animation: number-flip 2s ease-in-out infinite; }
.animate-shake { animation: shake 0.5s ease-in-out infinite; }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:text-5xl { font-size: 3rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:text-5xl { font-size: 3rem; }
  .lg\\:text-6xl { font-size: 3.75rem; }
}

/* ABOUT ME SECTION */
.profile-card {
  position: relative;
  animation: profileFloat 6s ease-in-out infinite;
  box-shadow:
    0 0 30px rgba(255, 0, 85, 0.3),
    inset 0 0 30px rgba(255, 0, 85, 0.1);
}

.profile-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: transparent;
    border-radius: 18px;
    z-index: -1;
    animation: borderGlow 3s linear infinite;
}

.about-content p {
  
  font-size: 1rem;
  line-height: 1.7;
}
button.w-full.mt-6.bg-squid-green.text-black.font-bold.py-3.rounded-lg.hover\:bg-squid-pink.hover\:text-white.transition-all.duration-300 .bg-squid-green{
    background-color: white !important;
}



.about-content span.text-squid-pink,
.about-content span.text-squid-green {
  text-shadow: 0 0 10px currentColor;
}

@keyframes profileFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes borderGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* ADVANCED DIGITAL CLOCK */
.digital-clock-container {
  position: relative;
}

.digital-clock-frame {
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border: 2px solid #333;
  border-radius: 8px;
  padding: 4px;
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.5);
}

.digital-clock-screen {
  background: #000;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 8px 12px;
  position: relative;
  overflow: hidden;
  min-width: 140px;
}

.digital-clock-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 1px;
  opacity: 0.1;
}

.clock-pixel {
  background: rgb(255, 0, 85);
  border-radius: 1px;
  animation: pixelFlicker 3s ease-in-out infinite;
}

.clock-pixel:nth-child(odd) {
  animation-delay: 0.5s;
}

.clock-pixel:nth-child(3n) {
  animation-delay: 1s;
}

.digital-clock-display {
  position: relative;
  z-index: 2;
  text-align: center;
}

#digital-time {
  font-family: 'Game Of Squids', 'Courier New', monospace;
  font-size: 1.2rem;
  font-weight: bold;
  color: rgb(255, 0, 85);
  text-shadow:
    0 0 5px rgb(255, 0, 85),
    0 0 10px rgb(255, 0, 85),
    0 0 15px rgb(255, 0, 85);
  letter-spacing: 2px;
  animation: digitalGlow 2s ease-in-out infinite alternate;
}

.clock-date {
  margin-top: 2px;
}

#digital-date {
  font-family: 'Game Of Squids', 'Courier New', monospace;
  font-size: 0.7rem;
  color: #00ff88;
  text-shadow: 0 0 3px #00ff88;
  letter-spacing: 1px;
  opacity: 0.9;
}

@keyframes pixelFlicker {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

@keyframes digitalGlow {
  from {
    text-shadow:
      0 0 5px rgb(255, 0, 85),
      0 0 10px rgb(255, 0, 85),
      0 0 15px rgb(255, 0, 85);
  }
  to {
    text-shadow:
      0 0 8px rgb(255, 0, 85),
      0 0 15px rgb(255, 0, 85),
      0 0 25px rgb(255, 0, 85);
  }
}

/* MOBILE RESPONSIVE IMPROVEMENTS */
@media (max-width: 768px) {
  /* Mobile-specific fixes */
  body {
    cursor: auto; /* Disable custom cursor on mobile */
  }

  .squid-cursor {
    display: none; /* Hide custom cursor on mobile */
  }

  /* Fix NFC button touch issues */
  .nfc-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
    position: relative;
    z-index: 10;
  }

  .nfc-button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Mobile navigation improvements */
  nav {
    padding: 0.75rem 1rem;
  }

  nav .flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* Mobile player number display - make it smaller and responsive */
  .digital-player-display {
    min-width: 70px !important;
    transform: translateX(-50%) scale(0.75) !important;
  }

  .digital-display {
    font-size: 1rem !important;
    padding: 0.4rem 0.6rem !important;
    letter-spacing: 1px !important;
  }

  /* Fix project card player text positioning on mobile */
  .project-card .absolute.top-4.left-4 {
    top: 0.75rem !important;
    left: 0.75rem !important;
    font-size: 1rem !important;
    line-height: 1.2 !important;
  }

  .skill-card .absolute.top-4.left-4 {
    top: 0.75rem !important;
    left: 0.75rem !important;
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
  }

  /* Mobile hero section */
  .hero-section h1,
  .glitch-text {
    font-size: 2.5rem !important;
    line-height: 1.1;
  }

  .hero-section .bg-squid-green {
    font-size: 0.9rem !important;
    padding: 0.4rem 0.8rem !important;
  }

  /* Mobile project cards */
  .project-card,
  .skill-card {
    height: 220px !important;
  }

  .project-card .text-2xl,
  .skill-card .text-2xl {
    font-size: 1.1rem !important;
  }

  .project-card .text-sm,
  .skill-card .text-sm {
    font-size: 0.8rem !important;
  }

  /* Mobile guards */
  .guard-figure-mobile {
    width: 3rem !important;
    height: 4.5rem !important;
  }

  .guard-body-mobile {
    width: 100%;
    height: 100%;
    background: rgb(255, 0, 85);
    border-radius: 1.5rem 1.5rem 0rem 0rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .guard-mask-triangular-mobile,
  .guard-mask-round-mobile {
    position: absolute;
    top: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5rem;
    height: 1.5rem;
    background: black;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .guard-mask-triangular-mobile::before {
    content: '';
    width: 0.6rem;
    height: 0.6rem;
    background: white;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    border: 1px solid white;
  }

  .guard-mask-round-mobile::before {
    content: '';
    width: 0.6rem;
    height: 0.6rem;
    background: white;
    border-radius: 50%;
  }

  .guard-eye-mobile {
    width: 0.15rem;
    height: 0.15rem;
    background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
    border-radius: 50%;
    animation: red-light-bloom 2s ease-in-out infinite;
    box-shadow:
      0 0 3px rgb(255, 0, 85),
      0 0 6px rgb(255, 0, 85),
      0 0 9px rgb(255, 0, 85);
  }

  /* Mobile contact form */
  .contact-form {
    padding: 1rem !important;
  }

  /* Mobile about section */
  .profile-card {
    margin-bottom: 2rem;
  }

  .about-content p {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  /* Mobile text sizes */
  .text-4xl { font-size: 1.8rem !important; }
  .text-5xl { font-size: 2.2rem !important; }
  .text-6xl { font-size: 2.8rem !important; }
  .text-8xl { font-size: 3.5rem !important; }
  .text-9xl { font-size: 4rem !important; }

  /* Mobile-specific improvements */
  .mobile-device .card-3d:hover {
    transform: none !important;
    box-shadow: none !important;
  }

  .mobile-device .project-card:hover,
  .mobile-device .skill-card:hover {
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
  }

  /* Better mobile scrolling */
  body.mobile-device {
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
  }

  /* Mobile navigation symbols */
  nav .text-squid-pink a {
    padding: 0.5rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
  }

  /* Mobile digital clock adjustments */
  .digital-clock-container {
    transform: scale(0.8);
    transform-origin: center;
  }

  .digital-clock-screen {
    min-width: 100px !important;
    padding: 4px 8px !important;
  }

  #digital-time {
    font-size: 0.9rem !important;
    letter-spacing: 1px !important;
  }

  #digital-date {
    font-size: 0.5rem !important;
  }

  /* Mobile social links */
  .social-link {
    min-width: 48px !important;
    min-height: 48px !important;
  }

  /* Mobile form improvements */
  input, textarea, button {
    font-size: 16px !important; /* Prevent zoom on iOS */
  }

  /* Mobile loading overlay */
  .premium-loading-overlay .loading-text {
    font-size: 1.2rem !important;
  }

  .premium-loading-overlay .loading-destination {
    font-size: 1.5rem !important;
  }
}

/* Additional mobile improvements for very small screens */
@media (max-width: 480px) {
  .hero-section h1,
  .glitch-text {
    font-size: 2rem !important;
  }

  .hero-section .bg-squid-green {
    font-size: 0.8rem !important;
    padding: 0.3rem 0.6rem !important;
  }

  .digital-player-display {
    transform: translateX(-50%) scale(0.65) !important;
    min-width: 60px !important;
  }

  .digital-display {
    font-size: 0.8rem !important;
    padding: 0.3rem 0.5rem !important;
  }

  /* Further reduce project card player text on very small screens */
  .project-card .absolute.top-4.left-4 {
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.8rem !important;
  }

  .skill-card .absolute.top-4.left-4 {
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.65rem !important;
  }

  .project-card,
  .skill-card {
    height: 200px !important;
  }

  .guard-figure-mobile {
    width: 2.5rem !important;
    height: 3.5rem !important;
  }

  nav {
    padding: 0.5rem !important;
  }

  .text-4xl { font-size: 1.5rem !important; }
  .text-5xl { font-size: 1.8rem !important; }
  .text-6xl { font-size: 2.2rem !important; }

  /* Very small screen digital clock */
  .digital-clock-container {
    transform: scale(0.65) !important;
  }

  .digital-clock-screen {
    min-width: 80px !important;
    padding: 2px 4px !important;
  }

  #digital-time {
    font-size: 0.7rem !important;
  }

  #digital-date {
    font-size: 0.4rem !important;
  }
}
